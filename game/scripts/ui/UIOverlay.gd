extends CanvasLayer

# References to UI nodes (assign in the editor using unique names %)
@onready var player_name_label: Label = %PlayerNameLabel
@onready var player_id_label: Label = %PlayerIDLabel
@onready var avatar_texture_rect: TextureRect = %AvatarTextureRect
@onready var follow_player_button: Button = %FollowPlayerButton
@onready var build_button: Button = %BuildButton
@onready var resource_container: HBoxContainer = %ResourceContainer

# Menu related nodes
@onready var menu_button: Button = %MenuButton
@onready var slide_menu: PanelContainer = %SlideMenu
@onready var hero_button: Button = %HeroButton
@onready var dashboard_button: Button = %DashboardButton
@onready var logout_button: Button = %LogoutButton

@onready var avatar_http_request: HTTPRequest = %AvatarHttpRequest # For downloading avatar

# Menu state
var menu_open: bool = false

# State for action buttons logic
var current_target_territory_id: int = -1 # ID of the territory the player can currently capture
var current_target_resource_id: int = -1 # ID of the resource the player can currently gather (New)

# Added: Reference to MapViewer
var map_viewer: MapViewer = null

func _ready():
	GameState.player_data_updated.connect(_on_player_data_updated)
	GameState.location_updated.connect(_on_player_location_updated)

	# Update with initial state if available on ready
	_on_player_data_updated(GameState.get_player_state())

	# Update button state initially
	_on_player_location_updated(GameState.get_player_location().x, GameState.get_player_location().y)

	# Initialize slide menu (hidden by default)
	if slide_menu:
		slide_menu.visible = true
		# Make sure it starts off-screen
		slide_menu.position.x = -300
	else:
		Logger.error("UIOverlay", "SlideMenu node not found!")

# Connect API signals
	if KokumeApi:
		KokumeApi.region_occupied.connect(_on_territory_captured)
		KokumeApi.region_occupation_failed.connect(_on_territory_capture_failed)
		KokumeApi.building_resources_picked_up.connect(_on_resources_gathered)
		KokumeApi.building_resource_pickup_failed.connect(_on_resource_gathering_failed)
	else:
		Logger.error("UIOverlay", "KokumeApi autoload not found!")

	# Connect button signals
	if follow_player_button:
		follow_player_button.pressed.connect(_on_follow_player_button_pressed)
	else:
		Logger.error("UIOverlay", "FollowPlayerButton node not found!")

	if build_button:
		build_button.pressed.connect(_on_build_button_pressed)
	else:
		Logger.error("UIOverlay", "BuildButton node not found!")

	# Connect menu button signals
	if menu_button:
		menu_button.pressed.connect(_on_menu_button_pressed)
	else:
		Logger.error("UIOverlay", "MenuButton node not found!")

	if hero_button:
		hero_button.pressed.connect(_on_hero_button_pressed)
	else:
		Logger.error("UIOverlay", "HeroButton node not found!")

	if dashboard_button:
		dashboard_button.pressed.connect(_on_dashboard_button_pressed)
	else:
		Logger.error("UIOverlay", "DashboardButton node not found!")

	if logout_button:
		logout_button.pressed.connect(_on_logout_button_pressed)
	else:
		Logger.error("UIOverlay", "LogoutButton node not found!")

	# Connect Avatar download signal
	if not avatar_http_request:
		Logger.error("UIOverlay", "AvatarHttpRequest node not found!")
	else:
		avatar_http_request.request_completed.connect(_on_avatar_download_completed)


func _on_player_data_updated(player_state: Dictionary):
	if player_name_label:
		player_name_label.text = player_state.get("nickname", "N/A")
	if player_id_label:
		player_id_label.text = "ID: %s" % player_state.get("id", "N/A")

	# Update resources display
	var resources = player_state.get("resources", {})
	# Clear previous resource entries
	for child in resource_container.get_children():
		child.queue_free()
	# Populate with new resources
	for resource in resources:
		var amount = resource.amount
		var type = resource.type

		var icon = "❔"
		match type.slug:
			"food": icon = "F"
			"wood": icon = "W"
			"clay": icon = "C"
			"stone": icon = "S"
			"iron": icon = "I"
			"gold": icon = "G"

		var resource_label = Label.new()
		resource_label.text = "%s: %d" % [icon, amount]
		resource_container.add_child(resource_label)

	# Handle avatar
	var avatar_url = player_state.get("avatar_url", "")
	if avatar_texture_rect and not avatar_url.is_empty():
		_download_avatar(avatar_url)
	elif avatar_texture_rect:
		avatar_texture_rect.texture = null


func _on_player_location_updated(latitude: float, longitude: float, _accuracy: float = 0.0, _speed: float = 0.0):
	# Update state for both capture and gather buttons based on proximity
	current_target_territory_id = -1
	current_target_resource_id = -1
	var _can_capture = false  # Used for future UI updates
	var _can_gather = false   # Used for future UI updates
	var player_loc = Vector2(latitude, longitude)

	if GameState:
		# Check Territories
		var nearby_territories = GameState.nearby_territories
		var closest_terr_dist_sq = INF
		for territory in nearby_territories:
			var terr_lat = territory.get("latitude", null)
			var terr_lon = territory.get("longitude", null)
			var terr_id = territory.get("id", -1)
			var owner_id = territory.get("owner_id", null)

			if terr_lat != null and terr_lon != null and terr_id != -1 and owner_id != GameState.player_id:
				var terr_loc = Vector2(terr_lat, terr_lon)
				var dist_sq = player_loc.distance_squared_to(terr_loc)
				var capture_radius_sq = 0.00001 # Example threshold
				if dist_sq < capture_radius_sq and dist_sq < closest_terr_dist_sq:
					closest_terr_dist_sq = dist_sq
					current_target_territory_id = terr_id
					_can_capture = true

		# Check Resources (New)
		var nearby_resources = GameState.nearby_resources
		var closest_res_dist_sq = INF
		for resource in nearby_resources:
			# Assuming resource has 'latitude', 'longitude', 'id'
			var res_lat = resource.get("latitude", null)
			var res_lon = resource.get("longitude", null)
			var res_id = resource.get("id", -1)

			if res_lat != null and res_lon != null and res_id != -1:
				var res_loc = Vector2(res_lat, res_lon)
				var dist_sq = player_loc.distance_squared_to(res_loc)
				var gather_radius_sq = 0.000005 # Example threshold (likely smaller than capture)
				if dist_sq < gather_radius_sq and dist_sq < closest_res_dist_sq:
					closest_res_dist_sq = dist_sq
					current_target_resource_id = res_id
					_can_gather = true

func _on_follow_player_button_pressed():
	GameState.follow_gps = not GameState.follow_gps
	follow_player_button.text = "GPS lock:\nON" if GameState.follow_gps else "GPS lock:\nOFF"


func _on_build_button_pressed():
	Logger.info("UIOverlay", "Build button pressed")
	if UIManager:
		UIManager.show_build_modal()
	else:
		Logger.error("UIOverlay", "UIManager not found")


func _on_territory_captured(data: Dictionary):
	Logger.info("UIOverlay", "Territory capture successful! Data: %s" % str(data))
	# Re-evaluate button states based on current location after action
	if GameState and GpsService and GpsService.is_active():
		_on_player_location_updated(GameState.get_player_location().x, GameState.get_player_location().y)
	# TODO: Show success feedback


func _on_territory_capture_failed(response_code: int, error_data: Dictionary):
	Logger.error("UIOverlay", "Territory capture failed! Code: %d, Error: %s" % [response_code, str(error_data)])
	# Re-evaluate button states
	if GameState and GpsService and GpsService.is_active():
		_on_player_location_updated(GameState.get_player_location().x, GameState.get_player_location().y)
	# TODO: Show error feedback


func _on_resources_gathered(data: Dictionary): # New callback
	Logger.info("UIOverlay", "Resource gathering successful! Data: %s" % str(data))
	# Re-evaluate button states
	if GameState and GpsService and GpsService.is_active():
		_on_player_location_updated(GameState.get_player_location().x, GameState.get_player_location().y)
	# TODO: Show success feedback (e.g., "+1 Wood")


func _on_resource_gathering_failed(response_code: int, error_data: Dictionary): # New callback
	Logger.error("UIOverlay", "Resource gathering failed! Code: %d, Error: %s" % [response_code, str(error_data)])
	# Re-evaluate button states
	if GameState and GpsService and GpsService.is_active():
		_on_player_location_updated(GameState.get_player_location().x, GameState.get_player_location().y)
	# TODO: Show error feedback


func _download_avatar(url: String):
	if not avatar_http_request:
		Logger.error("UIOverlay", "Cannot download avatar, HTTPRequest node missing.")
		return

	if avatar_http_request.get_meta("downloading_url", "") == url: return
	if avatar_texture_rect.texture and avatar_http_request.download_file == url: return

	Logger.info("UIOverlay", "Downloading avatar from: %s" % url)
	avatar_http_request.set_meta("downloading_url", url)
	var http_error = avatar_http_request.request(url)
	if http_error != OK:
		Logger.error("UIOverlay", "An error occurred starting the avatar HTTP request: %s" % http_error)
		avatar_http_request.set_meta("downloading_url", "")


func _on_avatar_download_completed(result, response_code, _headers, body: PackedByteArray):
	var requested_url = avatar_http_request.get_meta("downloading_url", "")
	avatar_http_request.set_meta("downloading_url", "")

	if result != HTTPRequest.RESULT_SUCCESS or response_code >= 300:
		Logger.error("UIOverlay", "Avatar download failed. Code: %d" % response_code)
		if avatar_texture_rect: avatar_texture_rect.texture = null
		return

	var image = Image.new()
	var image_error = image.load_webp_from_buffer(body)
	if image_error != OK:
		image_error = image.load_png_from_buffer(body)
		if image_error != OK:
			Logger.error("UIOverlay", "Failed to load avatar image. Error: %s" % image_error)
			if avatar_texture_rect: avatar_texture_rect.texture = null
			return

	var texture = ImageTexture.create_from_image(image)
	if avatar_texture_rect:
		avatar_texture_rect.texture = texture
		avatar_http_request.download_file = requested_url
	Logger.info("UIOverlay", "Avatar updated.")


# --- Menu Functions ---

func _on_menu_button_pressed():
	menu_open = !menu_open
	_toggle_menu(menu_open)


func _toggle_menu(open: bool):
	if not slide_menu:
		return

	var tween = create_tween()
	if open:
		# Slide in
		tween.tween_property(slide_menu, "position:x", 0, 0.3).set_trans(Tween.TRANS_CUBIC).set_ease(Tween.EASE_OUT)
	else:
		# Slide out
		tween.tween_property(slide_menu, "position:x", -300, 0.3).set_trans(Tween.TRANS_CUBIC).set_ease(Tween.EASE_IN)


func _on_hero_button_pressed():
	Logger.info("UIOverlay", "Hero button pressed")
	_toggle_menu(false)
	if UIManager:
		UIManager.show_hero_modal()
	else:
		Logger.error("UIOverlay", "UIManager not found")


func _on_dashboard_button_pressed():
	Logger.info("UIOverlay", "Dashboard button pressed")
	_toggle_menu(false)
	if UIManager:
		UIManager.show_dashboard_modal()
	else:
		Logger.error("UIOverlay", "UIManager not found")


func _on_logout_button_pressed():
	Logger.info("UIOverlay", "Logout button pressed")
	_toggle_menu(false)

	# Show confirmation dialog
	var confirmation = ConfirmationDialog.new()
	confirmation.title = "Logout Confirmation"
	confirmation.dialog_text = "Are you sure you want to logout?"
	confirmation.confirmed.connect(func():
		if UIManager:
			UIManager.logout()
		else:
			Logger.error("UIOverlay", "UIManager not found")
	)
	add_child(confirmation)
	confirmation.popup_centered()
